# Malou API MCP Server

A Model Context Protocol (MCP) server that provides access to <PERSON><PERSON>'s API for fetching restaurant reviews, managing medias, and creating GMB posts.

## Features

- **get_reviews**: Fetch reviews from Malou's API with comprehensive filtering options
- **get_restaurant_medias**: Get medias for a restaurant from Malou API
- **create_post**: Create a new post for a restaurant
- **prepare_post**: Prepare a post with content and settings for GMB publishing

## Installation

1. Build the server:

```bash
npm run build
```

2. The server will be available as `malou-api` command in the build directory.

## Inspect

```npx @modelcontextprotocol/inspector node FULL_PATH_TO_FOLDER/build/index.js

```

## Usage

### get_reviews Tool

Fetch reviews from Malou's API for one or more restaurants.

**Parameters:**

- `restaurant_ids` (required): Array of restaurant IDs to fetch reviews for
- `page_number` (optional): Page number (0-based, default: 0)
- `page_size` (optional): Number of reviews per page (1-100, default: 20)
- `text` (optional): Search text to filter reviews
- `ratings` (optional): Array of ratings to filter by (0-5)
- `start_date` (optional): Start date filter (ISO string)
- `end_date` (optional): End date filter (ISO string)
- `platforms` (optional): Array of platform keys to filter by
- `answered` (optional): Filter by answered reviews
- `not_answered` (optional): Filter by not answered reviews
- `pending` (optional): Filter by pending reviews
- `archived` (optional): Filter by archived reviews
- `unarchived` (optional): Filter by unarchived reviews
- `with_text` (optional): Filter reviews with text
- `without_text` (optional): Filter reviews without text
- `show_private` (optional): Include private reviews
- `answerable` (optional): Filter by answerable reviews

**Example:**

```json
{
    "restaurant_ids": ["507f1f77bcf86cd799439011"],
    "page_size": 10,
    "ratings": [4, 5],
    "with_text": true
}
```

### get_restaurant_medias Tool

Get medias for a restaurant from Malou API.

**Parameters:**

- `restaurant_id` (required): Restaurant ID to fetch medias for
- `page_number` (optional): Page number (0-based)
- `page_size` (optional): Number of medias per page (1-100)
- `media_type` (optional): Media type filter (photo, video, all)
- `title` (optional): Search by media title

**Example:**

```json
{
    "restaurant_id": "507f1f77bcf86cd799439011",
    "page_size": 10,
    "media_type": "photo"
}
```

### create_post Tool

Create a new post for a restaurant.

**Parameters:**

- `restaurant_id` (required): Restaurant ID to create post for
- `keys` (required): Platform keys (e.g., ["gmb", "facebook", "instagram"])
- `post_type` (optional): Post type (default: "post")
- `is_story` (optional): Whether this is a story (default: false)

**Example:**

```json
{
    "restaurant_id": "507f1f77bcf86cd799439011",
    "keys": ["gmb"]
}
```

### prepare_post Tool

Prepare a post with content and settings.

**Parameters:**

- `post_id` (required): Post ID to prepare
- `text` (optional): Post text content
- `language` (optional): Post language (e.g., "fr", "en")
- `planned_publication_date` (optional): Planned publication date (ISO string)
- `attachments` (optional): Array of media IDs to attach
- `attachments_name` (optional): Name for the attachments
- `call_to_action` (optional): Call to action configuration with `action_type` and `url`
- `post_topic` (optional): Post topic (e.g., "STANDARD")
- `event` (optional): Event configuration with `title`, `start_date`, and `end_date`
- `offer` (optional): Offer configuration with `coupon_code`, `online_url`, and `terms_conditions`
- `keys` (optional): Platform keys to publish to
- `draft` (optional): Whether to save as draft (default: true)

**Example:**

```json
{
    "post_id": "507f1f77bcf86cd799439012",
    "text": "Découvrez nos nouvelles options véganes!",
    "language": "fr",
    "planned_publication_date": "2025-10-02T16:15:34.710Z",
    "attachments": ["67d4432380974800018ac9c6"],
    "attachments_name": "restaurant pizza - vegan options",
    "call_to_action": {
        "action_type": "LEARN_MORE",
        "url": "https://bonnefourchette.com"
    },
    "post_topic": "STANDARD",
    "keys": ["gmb"],
    "draft": true
}
```

## GMB Post Creation Workflow

To create a complete GMB post from nothing:

1. **Get restaurant medias** to find available media for attachments:

    ```
    get_restaurant_medias(restaurant_id="your_restaurant_id")
    ```

2. **Create an empty post**:

    ```
    create_post(restaurant_id="your_restaurant_id", keys=["gmb"])
    ```

3. **Prepare the post** with content and settings:
    ```
    prepare_post(post_id="created_post_id", text="Your content", draft=true)
    ```

## Configuration

The server is configured to connect to the Malou API at `http://localhost:3000/api`. You can modify the `MALOU_API_BASE` constant in the source code to point to a different API endpoint.

## API Endpoint

The server uses the `/reviews/v2` endpoint from the Malou API, which corresponds to the `handleGetRestaurantsReviewsV2` controller method.

## Development

To modify the server:

1. Edit `src/index.ts`
2. Run `npm run build` to compile
3. Test the server with your MCP client

## Response Format

The `get_reviews` tool returns formatted text containing:

- Pagination information
- Review details including:
    - Review ID
    - Restaurant ID
    - Rating
    - Platform
    - Creation date
    - Status (answered/pending/not answered)
    - Archive status
    - Review text (truncated to 200 characters)
