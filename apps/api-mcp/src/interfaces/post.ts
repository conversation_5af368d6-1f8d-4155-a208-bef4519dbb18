export interface MalouPost {
    _id: string;
    text?: string;
    language?: string;
    plannedPublicationDate: string;
    attachments?: string[];
    attachmentsName?: string;
    callToAction?: {
        actionType: string;
        url: string;
    };
    postTopic?: string;
    event?: {
        title: string;
        startDate: string;
        endDate: string;
    };
    offer?: {
        couponCode: string;
        onlineUrl: string;
        termsConditions: string;
    };
    hashtags?: {
        selected: any[];
        suggested: any[];
    };
    keys: string[];
    socialAttachments?: any[];
}
