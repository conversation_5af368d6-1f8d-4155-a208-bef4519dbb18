import Config from './config.js';

export async function makeMalouRequest<T>(url: string, options?: RequestInit): Promise<T | null> {
    const headers = {
        'User-Agent': Config.MALOU_USER_AGENT,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...options?.headers,
        Authorization: Config.AuthorizationHeader,
    };

    try {
        const response = await fetch(url, { ...options, headers });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return (await response.json()) as T;
    } catch (error) {
        console.error('Error making <PERSON><PERSON> request:', error);
        return null;
    }
}
