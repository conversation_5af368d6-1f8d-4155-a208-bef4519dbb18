import { CreatePostBodyDtoInput } from '@malou-io/package-dto';
import { PlatformKey, PostSource, PostType } from '@malou-io/package-utils';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import z from 'zod';
import { MalouPost } from '../interfaces/post.js';
import Config from '../shared/config.js';
import { makeMalouRequest } from '../shared/make-request.js';

export const registerCreatePostTool = (server: McpServer) => {
    server.tool(
        'create_post',
        'Create a new post for a restaurant',
        {
            restaurant_id: z.string().describe('Restaurant ID to create post for'),
            keys: z
                .array(z.enum([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK, PlatformKey.MAPSTR, PlatformKey.GMB, PlatformKey.TIKTOK]))
                .describe('Platform keys (e.g., ["gmb", "facebook", "instagram"])'),
            post_type: z.nativeEnum(PostType).optional().describe('Post type (default: "post")'),
            is_story: z.boolean().optional().describe('Whether this is a story (default: false)'),
        },
        async (params) => {
            try {
                const postData: CreatePostBodyDtoInput = {
                    keys: params.keys,
                    post: {
                        text: '',
                        createdAt: new Date().toISOString(),
                        plannedPublicationDate: new Date().toISOString(),
                        postType: params.post_type || PostType.IMAGE,
                        isStory: params.is_story || false,
                        source: PostSource.SEO,
                    },
                };

                const createUrl = `${Config.MALOU_API_BASE}/posts/restaurants/${params.restaurant_id}`;
                const result: { data: MalouPost } | null = await makeMalouRequest<{ data: MalouPost }>(createUrl, {
                    method: 'POST',
                    body: JSON.stringify(postData),
                });

                if (!result) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: 'Failed to create post',
                            },
                        ],
                    };
                }

                return {
                    content: [
                        {
                            type: 'text',
                            text: `Post created successfully!\nPost ID: ${result.data._id}\nKeys: ${result.data.keys.join(', ')}\nPlanned publication: ${result.data.plannedPublicationDate}`,
                        },
                    ],
                };
            } catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error creating post: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        },
                    ],
                };
            }
        }
    );
};
