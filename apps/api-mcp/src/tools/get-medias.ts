import { MediaType } from '@malou-io/package-utils';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import z from 'zod';
import Config from '../shared/config.js';
import { makeMalouRequest } from '../shared/make-request.js';

interface MalouMedia {
    id: string;
    name?: string;
    title?: string;
    description?: string;
    category: string;
    format: string;
    type: string;
    duration?: number;
    urls: {
        original?: string;
        small?: string;
        cover?: string;
        smallCover?: string;
        igFit?: string;
    };
    dimensions?: {
        original?: { width: number; height: number };
        small?: { width: number; height: number };
        cover?: { width: number; height: number };
        smallCover?: { width: number; height: number };
        igFit?: { width: number; height: number };
    };
    createdAt?: string;
    updatedAt?: string;
}

export const registerGetMediasTool = (server: McpServer) => {
    server.tool(
        'get_restaurant_medias',
        'Get medias for a restaurant from Malou API',
        {
            restaurant_id: z.string().describe('Restaurant ID to fetch medias for'),
            page_number: z.number().int().gte(0).optional().describe('Page number (0-based)'),
            page_size: z.number().int().gte(1).lte(100).optional().describe('Number of medias per page (1-100)'),
            media_type: z
                .string()
                .refine((value) => [...Object.values(MediaType), 'all'].includes(value))
                .optional()
                .describe('Media type filter (photo, video, file)'),
            title: z.string().optional().describe('Search by media title'),
        },
        async (params) => {
            try {
                const queryParams = new URLSearchParams();

                if (params.page_number !== undefined) queryParams.set('page_number', params.page_number.toString());
                if (params.page_size !== undefined) queryParams.set('page_size', params.page_size.toString());
                queryParams.set('media_type', params.media_type ?? 'all');
                if (params.title) queryParams.set('title', params.title);

                // Set defaults
                queryParams.set('total', '0');
                queryParams.set('sort_order', '-1');

                const mediasUrl = `${Config.MALOU_API_BASE}/media/restaurants/${params.restaurant_id}?${queryParams.toString()}`;
                const mediasData: { data: { medias: MalouMedia[]; pagination: any } } | null = await makeMalouRequest<{
                    data: { medias: MalouMedia[]; pagination: any };
                }>(mediasUrl);

                if (!mediasData) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: 'Failed to retrieve medias data from Malou API',
                            },
                        ],
                    };
                }

                const { medias } = mediasData.data;

                if (medias.length === 0) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `No medias found for restaurant ${params.restaurant_id}`,
                            },
                        ],
                    };
                }

                const formattedMedias = medias.map((media) => {
                    const parts = [
                        `Media ID: ${media.id}`,
                        `Type: ${media.type}`,
                        `Format: ${media.format}`,
                        `Title: ${media.title || 'No title'}`,
                        `Description: ${media.description || 'No description'}`,
                    ];

                    if (media.urls.original) {
                        parts.push(`URL: ${media.urls.original}`);
                    }

                    if (media.dimensions?.original) {
                        parts.push(`Dimensions: ${media.dimensions.original.width}x${media.dimensions.original.height}`);
                    }

                    return parts.join('\n') + '\n---';
                });

                const mediasText = [`Medias for restaurant ${params.restaurant_id}:`, ``, ...formattedMedias].join('\n');

                return {
                    content: [
                        {
                            type: 'text',
                            text: mediasText,
                        },
                    ],
                };
            } catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error fetching medias: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        },
                    ],
                };
            }
        }
    );
};
