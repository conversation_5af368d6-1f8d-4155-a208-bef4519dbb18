import { GetRestaurantsReviewsV2ResponseDto } from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import z from 'zod';
import Config from '../shared/config.js';
import { makeMalouRequest } from '../shared/make-request.js';

export const registerGetReviewsTool = (server: McpServer) => {
    server.tool(
        'get_reviews',
        'Get reviews from Malou API for a restaurant. Fill in every parameters.',
        {
            restaurant_ids: z.array(z.string()).describe('Array of restaurant IDs to fetch reviews for'),
            page_number: z.number().int().gte(0).optional().describe('Page number (0-based)'),
            page_size: z.number().int().gte(1).lte(100).optional().describe('Number of reviews per page (1-100)'),
            text: z.string().optional().describe('Search text to filter reviews'),
            ratings: z.array(z.number().int().gte(0).lte(5)).optional().describe('Array of ratings to filter by (0-5)'),
            start_date: z.string().optional().describe('Start date filter (ISO string)'),
            end_date: z.string().optional().describe('End date filter (ISO string)'),
            platforms: z.array(z.string()).optional().describe('Array of platform keys to filter by'),
            answered: z.boolean().optional().describe('Filter by answered reviews'),
            not_answered: z.boolean().optional().describe('Filter by not answered reviews'),
            pending: z.boolean().optional().describe('Filter by pending reviews'),
            archived: z.boolean().optional().describe('Filter by archived reviews'),
            unarchived: z.boolean().optional().describe('Filter by unarchived reviews'),
            with_text: z.boolean().optional().describe('Filter reviews with text'),
            without_text: z.boolean().optional().describe('Filter reviews without text'),
            show_private: z.boolean().optional().describe('Include private reviews'),
            answerable: z.boolean().optional().describe('Filter by answerable reviews'),
        },
        async (params) => {
            try {
                // Build query parameters
                const queryParams = new URLSearchParams();

                // Required parameter
                params.restaurant_ids.forEach((id) => queryParams.append('restaurant_ids', id));

                // Optional parameters
                if (params.page_number !== undefined) queryParams.set('page_number', params.page_number.toString());
                if (params.page_size !== undefined) queryParams.set('page_size', params.page_size.toString());
                if (params.text) queryParams.set('text', params.text);
                if (params.ratings) params.ratings.forEach((rating) => queryParams.append('ratings', rating.toString()));
                if (params.start_date) queryParams.set('start_date', params.start_date);
                if (params.end_date) queryParams.set('end_date', params.end_date);
                if (params.platforms) params.platforms.forEach((platform) => queryParams.append('platforms', platform));
                if (params.answered !== undefined) queryParams.set('answered', params.answered.toString());
                if (params.not_answered !== undefined) queryParams.set('not_answered', params.not_answered.toString());
                if (params.pending !== undefined) queryParams.set('pending', params.pending.toString());
                if (params.archived !== undefined) queryParams.set('archived', params.archived.toString());
                if (params.unarchived !== undefined) queryParams.set('unarchived', params.unarchived.toString());
                if (params.with_text !== undefined) queryParams.set('with_text', params.with_text.toString());
                if (params.without_text !== undefined) queryParams.set('without_text', params.without_text.toString());
                if (params.show_private !== undefined) queryParams.set('show_private', params.show_private.toString());
                if (params.answerable !== undefined) queryParams.set('answerable', params.answerable.toString());

                const reviewsUrl = `${Config.MALOU_API_BASE}/reviews/v2?${queryParams.toString()}`;
                const reviewsData: ApiResultV2<GetRestaurantsReviewsV2ResponseDto> | null =
                    await makeMalouRequest<ApiResultV2<GetRestaurantsReviewsV2ResponseDto>>(reviewsUrl);

                if (!reviewsData) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: 'Failed to retrieve reviews data from Malou API',
                            },
                        ],
                    };
                }

                const { reviews, pagination } = reviewsData.data;

                if (reviews.length === 0) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `No reviews found for the specified criteria. Total: ${pagination}`,
                            },
                        ],
                    };
                }

                // Format reviews for display
                const formattedReviews = reviews.map((review) => {
                    const parts = [
                        `Review ID: ${review._id}`,
                        `Restaurant ID: ${review.restaurantId}`,
                        `Rating: ${review.rating || 'No rating'}`,
                        `Platform: ${review.key || 'Unknown'}`,
                        `Created: ${review.socialCreatedAt ? new Date(review.socialCreatedAt).toLocaleDateString() : 'Unknown'}`,
                        `Status: ${review.comments?.length > 0 ? 'Answered' : 'Not answered'}`,
                        `Archived: ${review.archived ? 'Yes' : 'No'}`,
                    ];

                    if (review.text) {
                        parts.push(`Text: ${review.text.substring(0, 200)}${review.text.length > 200 ? '...' : ''}`);
                    }

                    return parts.join('\n') + '\n---';
                });

                const reviewsText = [`Reviews for restaurants [${params.restaurant_ids.join(', ')}]:`, ``, ``, ...formattedReviews].join(
                    '\n'
                );

                return {
                    content: [
                        {
                            type: 'text',
                            text: reviewsText,
                        },
                    ],
                };
            } catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error fetching reviews: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        },
                    ],
                };
            }
        }
    );
};
